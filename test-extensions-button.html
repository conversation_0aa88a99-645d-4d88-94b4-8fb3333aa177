<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试更新扩展按钮</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .test-button {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #138496;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            background: #e9ecef;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>测试更新扩展按钮功能</h1>
        <p>这个页面用于测试新添加的"更新扩展"按钮功能。</p>
        
        <h2>功能说明</h2>
        <ul>
            <li>按钮位置：第五行右侧，替换了原来的"占位符"按钮</li>
            <li>按钮样式：使用info样式（蓝绿色边框）</li>
            <li>功能：点击后打开Chrome扩展页面 (chrome://extensions/)</li>
        </ul>

        <h2>测试步骤</h2>
        <ol>
            <li>启动ProfileLauncher应用程序</li>
            <li>在界面右侧找到"更新扩展"按钮</li>
            <li>点击按钮</li>
            <li>观察是否成功打开Chrome扩展页面</li>
            <li>检查日志区域是否显示相应的成功或错误消息</li>
        </ol>

        <h2>预期结果</h2>
        <ul>
            <li>✅ 按钮显示为"更新扩展"</li>
            <li>✅ 按钮使用蓝绿色边框样式</li>
            <li>✅ 点击后打开Chrome扩展页面</li>
            <li>✅ 日志显示"Chrome扩展页面已打开"</li>
        </ul>

        <h2>实现细节</h2>
        <div class="result">
            <strong>修改的文件：</strong><br>
            1. <code>assets/index.html</code> - 替换占位符按钮为更新扩展按钮<br>
            2. <code>src/renderer.js</code> - 添加openChromeExtensions函数<br>
            3. <code>src/main.js</code> - 添加IPC处理函数open-chrome-extensions
        </div>

        <button class="test-button" onclick="testFunction()">模拟测试</button>
        <div id="testResult" class="result" style="display: none;"></div>
    </div>

    <script>
        function testFunction() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result success';
            resultDiv.innerHTML = `
                <strong>模拟测试结果：</strong><br>
                ✅ 按钮已添加到界面<br>
                ✅ 样式设置正确<br>
                ✅ 事件处理函数已实现<br>
                ✅ IPC通信已配置<br>
                <br>
                <em>请在实际应用中测试完整功能</em>
            `;
        }
    </script>
</body>
</html>
