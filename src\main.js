const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');

// 恢复所有依赖项
const Config = require('./config');
const ChromeLauncher = require('./chrome-launcher');
const AutoRegister = require('./auto-register');

// 保持对窗口对象的全局引用
let mainWindow;
let pythonProcess;
let chromeLauncher;
let autoRegister;

function createWindow() {
    // 检查命令行参数
    const useMinWidth = process.argv.includes('--min-width');
    const isWindowed = process.argv.includes('--windowed');

    // 固定窗口大小为700px宽度
    const windowWidth = 700;
    const windowHeight = 800;

    // 创建浏览器窗口
    mainWindow = new BrowserWindow({
        width: windowWidth,
        height: windowHeight,
        minWidth: 700,
        minHeight: 600,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true
        },
        icon: path.join(__dirname, 'assets/icon.png'),
        show: false, // 先不显示，等加载完成后再显示
        backgroundColor: '#ffffff', // 设置背景色为白色
        autoHideMenuBar: true // 自动隐藏菜单栏
    });

    // 强制禁用缓存
    mainWindow.webContents.session.clearCache();
    mainWindow.webContents.session.clearStorageData();

    // 设置缓存禁用
    mainWindow.webContents.session.setUserAgent(mainWindow.webContents.session.getUserAgent() + ' NoCache');

    // 加载应用的 index.html
    mainWindow.loadFile('assets/index.html');

    // 窗口准备好后显示
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
    });

    // 初始化Chrome启动器
    chromeLauncher = new ChromeLauncher();
    chromeLauncher.setLogCallback((message, type) => {
        // 发送日志消息到渲染进程
        if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('log-message', { message, type });
        }
    });

    // 初始化自动注册器
    autoRegister = new AutoRegister();

    // 当窗口被关闭时发出事件
    mainWindow.on('closed', () => {
        mainWindow = null;
        // 关闭Python进程
        if (pythonProcess) {
            pythonProcess.kill();
        }
    });

    // 阻止新窗口打开，在默认浏览器中打开链接
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });
}

// Electron 初始化完成并准备创建浏览器窗口时调用
app.whenReady().then(createWindow);

// 当所有窗口都关闭时退出应用
app.on('window-all-closed', () => {
    // 在 macOS 上，应用和菜单栏通常会保持活动状态，直到用户明确退出
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    // 在 macOS 上，当点击 dock 图标并且没有其他窗口打开时，通常会重新创建一个窗口
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// IPC 通信处理












// Chrome配置文件检测功能（原get_profiles.py的Node.js实现）
function detectChromeProfiles() {
    try {
        const os = require('os');
        const userDataPath = path.join(os.homedir(), 'AppData', 'Local', 'Google', 'Chrome', 'User Data');

        if (!fs.existsSync(userDataPath)) {
            return [];
        }

        const profiles = [];

        // 检查Default配置文件
        const defaultPath = path.join(userDataPath, 'Default');
        if (fs.existsSync(defaultPath)) {
            const preferencesFile = path.join(defaultPath, 'Preferences');
            if (fs.existsSync(preferencesFile)) {
                profiles.push('Default');
            }
        }

        // 检查所有Profile配置文件
        try {
            const items = fs.readdirSync(userDataPath);
            for (const item of items) {
                const itemPath = path.join(userDataPath, item);
                if (fs.statSync(itemPath).isDirectory() && item.startsWith('Profile ')) {
                    const preferencesFile = path.join(itemPath, 'Preferences');
                    if (fs.existsSync(preferencesFile)) {
                        profiles.push(item);
                    }
                }
            }
        } catch (permissionError) {
            // 忽略权限错误，继续处理已找到的配置文件
        }

        // 去重并排序
        const uniqueProfiles = [...new Set(profiles)];

        // 自然排序函数
        const naturalSortKey = (profile) => {
            if (profile === 'Default') {
                return [0, 0];
            } else if (profile.startsWith('Profile ')) {
                try {
                    const num = parseInt(profile.split(' ')[1]);
                    return [1, num];
                } catch {
                    return [2, profile];
                }
            } else {
                return [2, profile];
            }
        };

        uniqueProfiles.sort((a, b) => {
            const keyA = naturalSortKey(a);
            const keyB = naturalSortKey(b);
            if (keyA[0] !== keyB[0]) return keyA[0] - keyB[0];
            if (typeof keyA[1] === 'number' && typeof keyB[1] === 'number') {
                return keyA[1] - keyB[1];
            }
            return keyA[1].toString().localeCompare(keyB[1].toString());
        });

        // 转换为显示格式
        const result = uniqueProfiles.map(profile => {
            let displayName;
            if (profile === 'Default') {
                displayName = '1';
            } else if (profile.startsWith('Profile ')) {
                try {
                    const profileNum = parseInt(profile.split(' ')[1]);
                    displayName = (profileNum + 1).toString();
                } catch {
                    displayName = profile;
                }
            } else {
                displayName = profile;
            }

            return {
                real_name: profile,
                display_name: displayName
            };
        });

        return result;
    } catch (error) {
        return [];
    }
}

ipcMain.handle('get-chrome-profiles', async () => {
    try {
        const profiles = detectChromeProfiles();
        return { success: true, profiles: profiles };
    } catch (error) {
        return { success: false, error: error.message };
    }
});

// 读取文件夹中账号数据的功能（原read_records.py的Node.js实现）
function readFolderAccountData(folderPath) {
    try {
        if (!fs.existsSync(folderPath) || !fs.statSync(folderPath).isDirectory()) {
            return null;
        }

        const files = fs.readdirSync(folderPath);
        const txtFiles = files.filter(file => file.endsWith('.txt'));

        if (txtFiles.length === 0) {
            return null;
        }

        const accountData = {};

        for (const file of txtFiles) {
            try {
                // 从文件名解析账号和天数信息
                // 格式: 账号名---天数#7.txt
                const match = file.match(/^(.+?)---(\d+)#\d+\.txt$/);
                if (match) {
                    const account = match[1].trim();
                    const days = parseInt(match[2]);

                    // 处理重复账号，选择最大天数
                    if (!accountData[account] || accountData[account] < days) {
                        accountData[account] = days;
                    }
                }
            } catch (fileError) {
                // 忽略文件处理错误
            }
        }

        return Object.keys(accountData).length > 0 ? accountData : null;
    } catch (error) {
        return null;
    }
}

// 检查账号是否达到最大天数
function checkIfMaxDaysReached(account, folderPath) {
    try {
        if (!fs.existsSync(folderPath)) {
            return false;
        }

        const files = fs.readdirSync(folderPath);
        const txtFiles = files.filter(file => file.endsWith('.txt'));

        for (const file of txtFiles) {
            const filePath = path.join(folderPath, file);
            try {
                const content = fs.readFileSync(filePath, 'utf8');
                const lines = content.split('\n').filter(line => line.trim());

                for (const line of lines) {
                    if (line.includes(account)) {
                        const match = line.match(/(\d+)#(\d+)/);
                        if (match) {
                            const currentDays = parseInt(match[1]);
                            const maxDays = parseInt(match[2]);
                            return currentDays >= maxDays;
                        }
                    }
                }
            } catch (fileError) {
                continue;
            }
        }

        return false;
    } catch {
        return false;
    }
}

// 对比今日和昨日文件的主要功能
function compareDailyFilesTableFormat() {
    try {
        const os = require('os');
        const userHome = os.homedir();
        const userRtbsDir = path.join(userHome, 'Downloads', 'RTBS');
        const stalkDir = fs.existsSync(userRtbsDir) ? userRtbsDir : 'C:\\Users\\<USER>\\Downloads\\RTBS';

        if (!fs.existsSync(stalkDir)) {
            return {
                success: false,
                message: `RTBS目录不存在: ${stalkDir}`,
                data: null
            };
        }

        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        // 使用中文日期格式
        const todayFolder = `${today.getMonth() + 1}月${today.getDate()}日`;
        const yesterdayFolder = `${yesterday.getMonth() + 1}月${yesterday.getDate()}日`;

        const todayPath = path.join(stalkDir, todayFolder);
        const yesterdayPath = path.join(stalkDir, yesterdayFolder);

        const todayData = readFolderAccountData(todayPath) || {};
        const yesterdayData = readFolderAccountData(yesterdayPath) || {};

        // 获取所有账号
        const allAccounts = new Set([...Object.keys(todayData), ...Object.keys(yesterdayData)]);

        const accounts = [];
        let index = 1;

        // 先收集所有账号数据
        const accountsData = [];
        for (const account of Array.from(allAccounts)) {
            // 处理今日数据：如果账号不在今日数据中，设为null（表示无数据）
            const todayDays = account in todayData ? todayData[account] : null;
            // 处理昨日数据：如果账号不在昨日数据中，设为null（表示无数据）
            const yesterdayDays = account in yesterdayData ? yesterdayData[account] : null;

            let status, statusClass;

            // 判断状态逻辑
            if (todayDays === null && yesterdayDays === null) {
                // 今日和昨日都没有数据
                status = '无记录';
                statusClass = 'new';
            } else if (todayDays === null) {
                // 今日没有数据，昨日有数据
                status = '空白数据';
                statusClass = 'empty';
            } else if (yesterdayDays === null) {
                // 昨日没有数据，今日有数据
                status = '新增';
                statusClass = 'new';
            } else if (todayDays === 7 && yesterdayDays === 7) {
                // 7/7 情况，显示领取奖励
                status = '领取奖励';
                statusClass = 'reward';
            } else if (todayDays > yesterdayDays) {
                // 正常增加
                status = `+${todayDays - yesterdayDays}`;
                statusClass = 'increased';
            } else if (todayDays === yesterdayDays && todayDays > 0) {
                // 天数相同且大于0
                if (checkIfMaxDaysReached(account, todayPath)) {
                    status = '已满';
                    statusClass = 'max';
                } else {
                    status = '无变化';
                    statusClass = 'unchanged';
                }
            } else if (todayDays < yesterdayDays) {
                // 天数减少
                status = `${todayDays - yesterdayDays}`;
                statusClass = 'decreased';
            } else {
                // 其他情况
                status = '未知';
                statusClass = 'new';
            }

            accountsData.push({
                account: account,
                today_days: todayDays,
                yesterday_days: yesterdayDays,
                status: status,
                status_class: statusClass
            });
        }

        // 按最大天数排序（今日和昨日的最大值），最大的排前面
        accountsData.sort((a, b) => {
            const maxA = Math.max(a.today_days || 0, a.yesterday_days || 0);
            const maxB = Math.max(b.today_days || 0, b.yesterday_days || 0);
            return maxB - maxA; // 降序排列，最大的在前面
        });

        // 添加序号
        for (let i = 0; i < accountsData.length; i++) {
            accounts.push({
                index: i + 1,
                ...accountsData[i]
            });
        }

        return {
            success: true,
            message: `成功读取记录，共 ${accounts.length} 个账号`,
            data: {
                today_date: today.toLocaleDateString('zh-CN', { month: 'long', day: 'numeric' }),
                yesterday_date: yesterday.toLocaleDateString('zh-CN', { month: 'long', day: 'numeric' }),
                accounts: accounts,
                file_name: `今日: ${todayFolder}, 昨日: ${yesterdayFolder}`
            }
        };

    } catch (error) {
        return {
            success: false,
            message: `程序执行失败: ${error.message}`,
            data: null
        };
    }
}

ipcMain.handle('read-records', async () => {
    try {
        return compareDailyFilesTableFormat();
    } catch (error) {
        return {
            success: false,
            message: error.message,
            data: null
        };
    }
});

// 设置无头模式
ipcMain.handle('set-headless-mode', async (event, enabled) => {
    try {
        if (chromeLauncher) {
            chromeLauncher.setHeadlessMode(enabled);
            return { success: true, message: `无头模式已${enabled ? '启用' : '禁用'}` };
        }
        return { success: false, message: 'Chrome启动器未初始化' };
    } catch (error) {
        return { success: false, message: error.message };
    }
});

// 设置最小化模式
ipcMain.handle('set-minimize-mode', async (event, enabled) => {
    try {
        if (chromeLauncher) {
            chromeLauncher.setMinimizeMode(enabled);
            return { success: true, message: `最小化模式已${enabled ? '启用' : '禁用'}` };
        }
        return { success: false, message: 'Chrome启动器未初始化' };
    } catch (error) {
        return { success: false, message: error.message };
    }
});

// 启动必应奖励 - 完全对应Python版本逻辑
ipcMain.handle('launch-bing-rewards', async (event, profiles) => {
    try {
        if (!chromeLauncher) {
            return { success: false, message: 'Chrome启动器未初始化' };
        }

        // 创建今日文件夹
        await chromeLauncher.createTodayFolder();

        // 设置并发数
        chromeLauncher.setMaxWorkers(Config.MAX_BING_WORKERS);

        // 定义单个配置文件的任务
        const launchTask = async (profileDisplay) => {
            const profileReal = Config.getProfileRealName(profileDisplay);
            try {
                await chromeLauncher.launchProfileWithWebsite(profileReal, Config.BING_REWARDS_URL);
                return { profile: profileDisplay, success: true };
            } catch (error) {
                return { profile: profileDisplay, success: false, error: error.message };
            }
        };

        // 使用并发执行
        const results = await chromeLauncher.executeWithConcurrency(profiles, launchTask, Config.MAX_BING_WORKERS);

        const successCount = results.filter(r => r.success).length;
        const failCount = results.length - successCount;

        // 在无头模式下，设置自动清理
        if (chromeLauncher.headlessMode) {
            chromeLauncher.log(`无头模式: 将在${Config.HEADLESS_CLEANUP_DELAY / 1000}秒后自动关闭必应奖励浏览器进程...`);
            setTimeout(() => {
                chromeLauncher.closeHeadlessProcesses();
            }, Config.HEADLESS_CLEANUP_DELAY);
        }

        return {
            success: true,
            message: `必应奖励启动完成 (成功: ${successCount}, 失败: ${failCount})`,
            results,
            successCount,
            failCount
        };
    } catch (error) {
        return {
            success: false,
            message: `必应奖励启动失败: ${error.message}`
        };
    }
});

// 启动哔哩搜索 - 完全对应Python版本逻辑（执行两次搜索）
ipcMain.handle('launch-bilibili-search', async (event, profiles) => {
    try {
        if (!chromeLauncher) {
            return { success: false, message: 'Chrome启动器未初始化' };
        }

        // 创建今日文件夹
        await chromeLauncher.createTodayFolder();

        // 设置并发数
        chromeLauncher.setMaxWorkers(Config.MAX_BILIBILI_WORKERS);

        // 定义单个配置文件的任务
        const searchTask = async (profileDisplay) => {
            const profileReal = Config.getProfileRealName(profileDisplay);
            try {
                await chromeLauncher.executeBilibiliSearchFlow(profileReal);
                // 添加配置文件间延迟
                await chromeLauncher.delay(Config.BILIBILI_PROFILE_DELAY);
                return { profile: profileDisplay, success: true };
            } catch (error) {
                return { profile: profileDisplay, success: false, error: error.message };
            }
        };

        // 使用并发执行
        const results = await chromeLauncher.executeWithConcurrency(profiles, searchTask, Config.MAX_BILIBILI_WORKERS);

        const successCount = results.filter(r => r.success).length;
        const failCount = results.length - successCount;

        // 在无头模式下，设置自动清理
        if (chromeLauncher.headlessMode) {
            chromeLauncher.log(`无头模式: 将在${Config.HEADLESS_CLEANUP_DELAY / 1000}秒后自动关闭哔哩搜索浏览器进程...`);
            setTimeout(() => {
                chromeLauncher.closeHeadlessProcesses();
            }, Config.HEADLESS_CLEANUP_DELAY);
        }

        return {
            success: true,
            message: `哔哩搜索完成 (成功: ${successCount}, 失败: ${failCount})`,
            results,
            successCount,
            failCount
        };
    } catch (error) {
        return {
            success: false,
            message: `哔哩搜索失败: ${error.message}`
        };
    }
});

// 自动模式 - 完全对应Python版本逻辑（7步流程）
ipcMain.handle('auto-mode', async (event, profiles) => {
    try {
        if (!chromeLauncher) {
            return { success: false, message: 'Chrome启动器未初始化' };
        }

        // 创建今日文件夹
        await chromeLauncher.createTodayFolder();

        // 设置并发数
        chromeLauncher.setMaxWorkers(Config.MAX_AUTO_WORKERS);

        // 定义单个配置文件的任务
        const autoTask = async (profileDisplay) => {
            const profileReal = Config.getProfileRealName(profileDisplay);
            try {
                // 执行完整的7步自动化流程，完全对应Python版本
                await chromeLauncher.executeAutoModeFlow(profileReal);

                // 添加配置文件间延迟
                await chromeLauncher.delay(Config.AUTO_MODE_PROFILE_DELAY);

                return { profile: profileDisplay, success: true };
            } catch (error) {
                return { profile: profileDisplay, success: false, error: error.message };
            }
        };

        // 使用并发执行
        const results = await chromeLauncher.executeWithConcurrency(profiles, autoTask, Config.MAX_AUTO_WORKERS);

        const successCount = results.filter(r => r.success).length;
        const failCount = results.length - successCount;

        // 在无头模式下，设置自动清理
        if (chromeLauncher.headlessMode) {
            chromeLauncher.log(`无头模式: 将在${Config.HEADLESS_CLEANUP_DELAY / 1000}秒后自动关闭自动模式浏览器进程...`);
            setTimeout(() => {
                chromeLauncher.closeHeadlessProcesses();
            }, Config.HEADLESS_CLEANUP_DELAY);
        }

        return {
            success: true,
            message: `自动模式完成 (成功: ${successCount}, 失败: ${failCount})`,
            results,
            successCount,
            failCount
        };
    } catch (error) {
        return {
            success: false,
            message: `自动模式失败: ${error.message}`
        };
    }
});

// 清除今日缓存
ipcMain.handle('clear-today-cache', async () => {
    try {
        if (!chromeLauncher) {
            return { success: false, message: 'Chrome启动器未初始化' };
        }

        const result = await chromeLauncher.clearTodayCache();
        return result;
    } catch (error) {
        return { success: false, message: error.message };
    }
});

// 启动账号注册 - 完全对应Python版本逻辑
ipcMain.handle('launch-account-signup', async (event, profiles) => {
    try {
        if (!chromeLauncher) {
            return { success: false, message: 'Chrome启动器未初始化' };
        }

        // 设置并发数
        chromeLauncher.setMaxWorkers(Config.MAX_BING_WORKERS);

        // 定义单个配置文件的任务
        const signupTask = async (profileDisplay) => {
            const profileReal = Config.getProfileRealName(profileDisplay);
            try {
                await chromeLauncher.launchProfileWithWebsite(profileReal, Config.ACCOUNT_SIGNUP_URL);
                return { profile: profileDisplay, success: true };
            } catch (error) {
                return { profile: profileDisplay, success: false, error: error.message };
            }
        };

        // 使用并发执行
        const results = await chromeLauncher.executeWithConcurrency(profiles, signupTask, Config.MAX_BING_WORKERS);

        const successCount = results.filter(r => r.success).length;
        const failCount = results.length - successCount;

        return {
            success: true,
            message: `账号注册页面启动完成 (成功: ${successCount}, 失败: ${failCount})`,
            results,
            successCount,
            failCount
        };
    } catch (error) {
        return { success: false, message: error.message };
    }
});

// 关闭所有Chrome
ipcMain.handle('close-all-chrome', async () => {
    try {
        if (chromeLauncher) {
            await chromeLauncher.closeAllChrome();
            await chromeLauncher.closeHeadlessProcesses();
            return { success: true, message: '所有Chrome进程已关闭' };
        }
        return { success: false, message: 'Chrome启动器未初始化' };
    } catch (error) {
        return { success: false, message: error.message };
    }
});

// 打开Chrome扩展页面
ipcMain.handle('open-chrome-extensions', async (event, profiles) => {
    try {
        if (!chromeLauncher) {
            return { success: false, message: 'Chrome启动器未初始化' };
        }

        if (!profiles || profiles.length === 0) {
            return { success: false, message: '请至少选择一个配置文件' };
        }

        // 为每个配置文件打开扩展页面
        const results = [];
        for (const profileDisplay of profiles) {
            const profileReal = Config.getProfileRealName(profileDisplay);
            try {
                await chromeLauncher.launchChromeInstance(profileReal, 'chrome://extensions/');
                results.push({ profile: profileDisplay, success: true });
                // 添加延迟避免同时启动太多实例
                await new Promise(resolve => setTimeout(resolve, 500));
            } catch (error) {
                results.push({ profile: profileDisplay, success: false, error: error.message });
            }
        }

        const successCount = results.filter(r => r.success).length;
        const failCount = results.filter(r => !r.success).length;

        if (successCount > 0) {
            let message = `成功为 ${successCount} 个配置文件打开Chrome扩展页面`;
            if (failCount > 0) {
                message += `，${failCount} 个失败`;
            }
            return { success: true, message };
        } else {
            return { success: false, message: `所有配置文件都打开失败` };
        }
    } catch (error) {
        return { success: false, message: error.message };
    }
});

// 保存文件对话框
ipcMain.handle('show-save-dialog', async () => {
    const result = await dialog.showSaveDialog(mainWindow, {
        title: '保存日志',
        defaultPath: `ProfileLauncher_Log_${new Date().toISOString().slice(0, 10)}.txt`,
        filters: [
            { name: '文本文件', extensions: ['txt'] },
            { name: '所有文件', extensions: ['*'] }
        ]
    });
    return result;
});

// 保存文件
ipcMain.handle('save-file', async (event, filePath, content) => {
    try {
        const fs = require('fs').promises;
        await fs.writeFile(filePath, content, 'utf8');
        return { success: true };
    } catch (error) {
        return { success: false, error: error.message };
    }
});

// 防止应用被多次启动
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
    app.quit();
// 自动注册功能 - 集成RRR项目
ipcMain.handle('auto-register-account', async (event, options = {}) => {
    try {
        if (!autoRegister) {
            return { success: false, message: '自动注册器未初始化' };
        }

        const result = await autoRegister.executeAutoRegister({
            browserPath: options.browserPath || '',
            botProtectionWait: options.botProtectionWait || 60,
            maxCaptchaRetries: options.maxCaptchaRetries || 5,
            maxAccounts: 1 // 每次只注册一个账户
        });

        return {
            success: true,
            message: '账户注册成功',
            accounts: result.accounts,
            output: result.output
        };

    } catch (error) {
        return {
            success: false,
            message: `自动注册失败: ${error.message}`,
            error: error.message
        };
    }
});

// 检查自动注册状态
ipcMain.handle('auto-register-status', async () => {
    if (!autoRegister) {
        return { isRunning: false, hasProcess: false };
    }
    return autoRegister.getStatus();
});

// 停止自动注册
ipcMain.handle('stop-auto-register', async () => {
    if (!autoRegister) {
        return { success: false, message: '自动注册器未初始化' };
    }

    try {
        autoRegister.stopRegistration();
        return { success: true, message: '自动注册已停止' };
    } catch (error) {
        return { success: false, message: `停止失败: ${error.message}` };
    }
});

// 安装Python依赖
ipcMain.handle('install-python-dependencies', async () => {
    try {
        if (!autoRegister) {
            return { success: false, message: '自动注册器未初始化' };
        }

        const output = await autoRegister.installDependencies();
        return {
            success: true,
            message: 'Python依赖安装成功',
            output: output
        };
    } catch (error) {
        return {
            success: false,
            message: `依赖安装失败: ${error.message}`,
            error: error.message
        };
    }
});

} else {
    app.on('second-instance', () => {
        // 当运行第二个实例时，将焦点放在主窗口上
        if (mainWindow) {
            if (mainWindow.isMinimized()) mainWindow.restore();
            mainWindow.focus();
        }
    });
}
